'use client';

import { motion } from 'framer-motion';
import { Calendar, MapPin, Briefcase } from 'lucide-react';

const Experience = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  const experiences = [
    {
      title: 'Senior Frontend Developer',
      company: 'TechCorp Solutions',
      location: 'Remote',
      period: '2022 - Present',
      type: 'Full-time',
      description: 'Leading frontend development for enterprise applications, mentoring junior developers, and implementing modern React architectures.',
      achievements: [
        'Improved application performance by 40% through code optimization',
        'Led a team of 5 developers in rebuilding the main product dashboard',
        'Implemented design system that reduced development time by 30%',
        'Introduced TypeScript and modern testing practices'
      ],
      technologies: ['React', 'TypeScript', 'Next.js', 'Tailwind CSS', 'GraphQL']
    },
    {
      title: 'UI/UX Designer & Frontend Developer',
      company: 'Digital Innovations Inc.',
      location: 'New York, NY',
      period: '2021 - 2022',
      type: 'Full-time',
      description: 'Designed and developed user interfaces for web applications, conducted user research, and collaborated with product teams.',
      achievements: [
        'Designed and developed 15+ responsive web applications',
        'Increased user engagement by 25% through UX improvements',
        'Created comprehensive design system and component library',
        'Conducted user testing sessions and implemented feedback'
      ],
      technologies: ['React', 'JavaScript', 'Figma', 'Adobe XD', 'SCSS', 'Node.js']
    },
    {
      title: 'Frontend Developer',
      company: 'StartupXYZ',
      location: 'San Francisco, CA',
      period: '2020 - 2021',
      type: 'Full-time',
      description: 'Developed responsive web applications and collaborated with designers to implement pixel-perfect user interfaces.',
      achievements: [
        'Built and maintained 10+ client websites',
        'Implemented responsive design principles across all projects',
        'Collaborated with backend team to integrate REST APIs',
        'Optimized websites for SEO and performance'
      ],
      technologies: ['HTML', 'CSS', 'JavaScript', 'React', 'Bootstrap', 'WordPress']
    },
    {
      title: 'Junior Web Developer',
      company: 'WebDev Agency',
      location: 'Austin, TX',
      period: '2019 - 2020',
      type: 'Full-time',
      description: 'Started my professional journey developing websites and learning modern web development practices.',
      achievements: [
        'Completed 20+ client projects successfully',
        'Learned modern JavaScript frameworks and tools',
        'Contributed to team coding standards and best practices',
        'Participated in code reviews and team meetings'
      ],
      technologies: ['HTML', 'CSS', 'JavaScript', 'jQuery', 'PHP', 'MySQL']
    }
  ];

  return (
    <section id="experience" className="py-20 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-1/3 left-1/4 w-72 h-72 bg-accent/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/3 right-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-6">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          className="max-w-4xl mx-auto"
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              Work <span className="gradient-text">Experience</span>
            </h2>
            <p className="text-xl text-foreground/70 max-w-3xl mx-auto">
              My professional journey in web development and design, building expertise through diverse projects and teams.
            </p>
          </motion.div>

          {/* Timeline */}
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary via-secondary to-accent"></div>

            {/* Experience Items */}
            <div className="space-y-12">
              {experiences.map((experience, index) => (
                <motion.div
                  key={experience.title + experience.company}
                  variants={itemVariants}
                  className="relative pl-20"
                >
                  {/* Timeline Dot */}
                  <motion.div
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="absolute left-6 top-6 w-4 h-4 bg-primary rounded-full border-4 border-background shadow-lg"
                  />

                  {/* Experience Card */}
                  <motion.div
                    whileHover={{ scale: 1.02, y: -5 }}
                    className="glass p-8 rounded-3xl hover:bg-foreground/5 transition-all duration-300"
                  >
                    {/* Header */}
                    <div className="mb-6">
                      <div className="flex flex-wrap items-center gap-4 mb-2">
                        <h3 className="text-2xl font-bold text-foreground">
                          {experience.title}
                        </h3>
                        <span className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium">
                          {experience.type}
                        </span>
                      </div>
                      
                      <div className="flex flex-wrap items-center gap-4 text-foreground/70 mb-4">
                        <div className="flex items-center gap-2">
                          <Briefcase size={16} />
                          <span className="font-semibold text-foreground">{experience.company}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin size={16} />
                          <span>{experience.location}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar size={16} />
                          <span>{experience.period}</span>
                        </div>
                      </div>
                      
                      <p className="text-foreground/80 leading-relaxed">
                        {experience.description}
                      </p>
                    </div>

                    {/* Achievements */}
                    <div className="mb-6">
                      <h4 className="text-lg font-semibold mb-3 text-foreground">
                        Key Achievements:
                      </h4>
                      <ul className="space-y-2">
                        {experience.achievements.map((achievement, achievementIndex) => (
                          <motion.li
                            key={achievementIndex}
                            initial={{ opacity: 0, x: -20 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.3, delay: achievementIndex * 0.1 }}
                            viewport={{ once: true }}
                            className="flex items-start gap-3 text-foreground/80"
                          >
                            <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                            <span>{achievement}</span>
                          </motion.li>
                        ))}
                      </ul>
                    </div>

                    {/* Technologies */}
                    <div>
                      <h4 className="text-lg font-semibold mb-3 text-foreground">
                        Technologies Used:
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {experience.technologies.map((tech) => (
                          <span
                            key={tech}
                            className="px-3 py-1 bg-secondary/10 text-secondary rounded-full text-sm font-medium"
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Call to Action */}
          <motion.div
            variants={itemVariants}
            className="text-center mt-16"
          >
            <div className="glass p-8 rounded-3xl">
              <h3 className="text-2xl font-semibold mb-4 gradient-text">
                Let's Work Together
              </h3>
              <p className="text-foreground/80 mb-6 max-w-2xl mx-auto">
                I'm always interested in new opportunities and exciting projects. 
                Whether you're looking for a developer, designer, or both, let's discuss how we can create something amazing together.
              </p>
              <motion.a
                href="#contact"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center gap-2 px-8 py-4 bg-primary text-white rounded-full font-medium hover:bg-primary/90 transition-colors duration-200"
              >
                Get In Touch
              </motion.a>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Experience;
