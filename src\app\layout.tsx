import type { Metada<PERSON> } from "next";
import { <PERSON>, <PERSON>ei<PERSON>_Mono } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Bhupender Yadav - Web Developer & UI/UX Designer",
  description: "<PERSON><PERSON><PERSON> of <PERSON><PERSON><PERSON><PERSON> Yadav, a passionate web developer and UI/UX designer creating beautiful, functional digital experiences.",
  keywords: ["web developer", "UI/UX designer", "frontend developer", "React", "Next.js", "portfolio"],
  authors: [{ name: "<PERSON>hupen<PERSON> Yadav" }],
  creator: "Bhupender Yadav",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://bhupenderyadav.dev",
    title: "B<PERSON><PERSON>der Yadav - Web Developer & UI/UX Designer",
    description: "<PERSON><PERSON><PERSON> of <PERSON><PERSON><PERSON><PERSON> Yadav, a passionate web developer and UI/UX designer creating beautiful, functional digital experiences.",
    siteName: "Bhupender Yadav Portfolio",
  },
  twitter: {
    card: "summary_large_image",
    title: "Bhupender Yadav - Web Developer & UI/UX Designer",
    description: "Portfolio of Bhupender Yadav, a passionate web developer and UI/UX designer creating beautiful, functional digital experiences.",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${inter.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
