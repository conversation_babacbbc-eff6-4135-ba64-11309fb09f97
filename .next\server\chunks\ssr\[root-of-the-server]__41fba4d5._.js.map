{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/port/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Menu, X, Sun, Moon } from 'lucide-react';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isDarkMode, setIsDarkMode] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  useEffect(() => {\n    // Check for saved theme preference or default to system preference\n    const savedTheme = localStorage.getItem('theme');\n    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n    \n    if (savedTheme === 'dark' || (!savedTheme && systemPrefersDark)) {\n      setIsDarkMode(true);\n      document.documentElement.classList.add('dark');\n    }\n  }, []);\n\n  const toggleTheme = () => {\n    setIsDarkMode(!isDarkMode);\n    if (isDarkMode) {\n      document.documentElement.classList.remove('dark');\n      localStorage.setItem('theme', 'light');\n    } else {\n      document.documentElement.classList.add('dark');\n      localStorage.setItem('theme', 'dark');\n    }\n  };\n\n  const navItems = [\n    { name: 'Home', href: '#home' },\n    { name: 'About', href: '#about' },\n    { name: 'Skills', href: '#skills' },\n    { name: 'Projects', href: '#projects' },\n    { name: 'Experience', href: '#experience' },\n    { name: 'Contact', href: '#contact' },\n  ];\n\n  return (\n    <motion.header\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.5 }}\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        scrolled ? 'glass shadow-lg' : 'bg-transparent'\n      }`}\n    >\n      <nav className=\"container mx-auto px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo */}\n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            className=\"text-2xl font-bold gradient-text\"\n          >\n            BY\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <motion.a\n                key={item.name}\n                href={item.href}\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"text-foreground hover:text-primary transition-colors duration-200 font-medium\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {item.name}\n              </motion.a>\n            ))}\n          </div>\n\n          {/* Theme Toggle & Mobile Menu Button */}\n          <div className=\"flex items-center space-x-4\">\n            <motion.button\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={toggleTheme}\n              className=\"p-2 rounded-full glass hover:bg-primary/20 transition-colors duration-200\"\n              aria-label=\"Toggle theme\"\n            >\n              {isDarkMode ? <Sun size={20} /> : <Moon size={20} />}\n            </motion.button>\n\n            <motion.button\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"md:hidden p-2 rounded-full glass hover:bg-primary/20 transition-colors duration-200\"\n              aria-label=\"Toggle menu\"\n            >\n              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}\n            </motion.button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <motion.div\n          initial={{ opacity: 0, height: 0 }}\n          animate={{\n            opacity: isMenuOpen ? 1 : 0,\n            height: isMenuOpen ? 'auto' : 0,\n          }}\n          transition={{ duration: 0.3 }}\n          className=\"md:hidden overflow-hidden\"\n        >\n          <div className=\"pt-4 pb-2 space-y-2\">\n            {navItems.map((item) => (\n              <motion.a\n                key={item.name}\n                href={item.href}\n                whileHover={{ x: 10 }}\n                className=\"block py-2 px-4 text-foreground hover:text-primary hover:bg-primary/10 rounded-lg transition-all duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {item.name}\n              </motion.a>\n            ))}\n          </div>\n        </motion.div>\n      </nav>\n    </motion.header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,OAAO,GAAG;QAC/B;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mEAAmE;QACnE,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;QAEnF,IAAI,eAAe,UAAW,CAAC,cAAc,mBAAoB;YAC/D,cAAc;YACd,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;QACzC;IACF,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,cAAc,CAAC;QACf,IAAI,YAAY;YACd,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;YAC1C,aAAa,OAAO,CAAC,SAAS;QAChC,OAAO;YACL,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;YACvC,aAAa,OAAO,CAAC,SAAS;QAChC;IACF;IAEA,MAAM,WAAW;QACf;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAc,MAAM;QAAc;QAC1C;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAC,4DAA4D,EACtE,WAAW,oBAAoB,kBAC/B;kBAEF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,WAAU;sCACX;;;;;;sCAKD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,KAAK,IAAI;oCACf,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;oCACV,SAAS,IAAM,cAAc;8CAE5B,KAAK,IAAI;mCAPL,KAAK,IAAI;;;;;;;;;;sCAapB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEV,2BAAa,8OAAC,gMAAA,CAAA,MAAG;wCAAC,MAAM;;;;;6DAAS,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;8CAGhD,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;oCACV,cAAW;8CAEV,2BAAa,8OAAC,4LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;6DAAS,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;8BAMlD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBACP,SAAS,aAAa,IAAI;wBAC1B,QAAQ,aAAa,SAAS;oBAChC;oBACA,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCAEP,MAAM,KAAK,IAAI;gCACf,YAAY;oCAAE,GAAG;gCAAG;gCACpB,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,KAAK,IAAI;+BANL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAc9B;uCAEe", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/port/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ArrowDown, Github, Linkedin, Mail, Download } from 'lucide-react';\n\nconst Hero = () => {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        delayChildren: 0.3,\n        staggerChildren: 0.2,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        duration: 0.5,\n      },\n    },\n  };\n\n  return (\n    <section id=\"home\" className=\"min-h-screen flex items-center justify-center relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 -z-10\">\n        <div className=\"absolute top-1/4 left-1/4 w-72 h-72 bg-primary/20 rounded-full blur-3xl animate-float\"></div>\n        <div className=\"absolute bottom-1/4 right-1/4 w-96 h-96 bg-secondary/20 rounded-full blur-3xl animate-float\" style={{ animationDelay: '2s' }}></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-accent/20 rounded-full blur-3xl animate-float\" style={{ animationDelay: '4s' }}></div>\n      </div>\n\n      <div className=\"container mx-auto px-6 text-center\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          className=\"max-w-4xl mx-auto\"\n        >\n          {/* Greeting */}\n          <motion.div variants={itemVariants} className=\"mb-6\">\n            <span className=\"inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium\">\n              👋 Hello, I'm\n            </span>\n          </motion.div>\n\n          {/* Name */}\n          <motion.h1\n            variants={itemVariants}\n            className=\"text-5xl md:text-7xl font-bold mb-6\"\n          >\n            <span className=\"gradient-text\">Bhupender Yadav</span>\n          </motion.h1>\n\n          {/* Title */}\n          <motion.h2\n            variants={itemVariants}\n            className=\"text-2xl md:text-4xl font-light mb-8 text-foreground/80\"\n          >\n            Web Developer & UI/UX Designer\n          </motion.h2>\n\n          {/* Description */}\n          <motion.p\n            variants={itemVariants}\n            className=\"text-lg md:text-xl text-foreground/70 mb-12 max-w-2xl mx-auto leading-relaxed\"\n          >\n            I create beautiful, functional, and user-centered digital experiences. \n            Passionate about crafting clean code and intuitive designs that make a difference.\n          </motion.p>\n\n          {/* CTA Buttons */}\n          <motion.div\n            variants={itemVariants}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-16\"\n          >\n            <motion.a\n              href=\"#projects\"\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"px-8 py-4 bg-primary text-white rounded-full font-medium hover:bg-primary/90 transition-all duration-200 flex items-center gap-2 shadow-lg hover:shadow-xl\"\n            >\n              View My Work\n              <ArrowDown size={20} />\n            </motion.a>\n            \n            <motion.a\n              href=\"/resume.pdf\"\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"px-8 py-4 glass text-foreground rounded-full font-medium hover:bg-foreground/10 transition-all duration-200 flex items-center gap-2\"\n              download\n            >\n              Download CV\n              <Download size={20} />\n            </motion.a>\n          </motion.div>\n\n          {/* Social Links */}\n          <motion.div\n            variants={itemVariants}\n            className=\"flex justify-center space-x-6\"\n          >\n            <motion.a\n              href=\"https://github.com/bhupenderyadav\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              whileHover={{ scale: 1.2, y: -5 }}\n              whileTap={{ scale: 0.9 }}\n              className=\"p-3 glass rounded-full hover:bg-primary/20 transition-all duration-200\"\n              aria-label=\"GitHub\"\n            >\n              <Github size={24} />\n            </motion.a>\n            \n            <motion.a\n              href=\"https://linkedin.com/in/bhupenderyadav\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              whileHover={{ scale: 1.2, y: -5 }}\n              whileTap={{ scale: 0.9 }}\n              className=\"p-3 glass rounded-full hover:bg-primary/20 transition-all duration-200\"\n              aria-label=\"LinkedIn\"\n            >\n              <Linkedin size={24} />\n            </motion.a>\n            \n            <motion.a\n              href=\"mailto:<EMAIL>\"\n              whileHover={{ scale: 1.2, y: -5 }}\n              whileTap={{ scale: 0.9 }}\n              className=\"p-3 glass rounded-full hover:bg-primary/20 transition-all duration-200\"\n              aria-label=\"Email\"\n            >\n              <Mail size={24} />\n            </motion.a>\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 1.5, duration: 0.5 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <motion.div\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n          className=\"w-6 h-10 border-2 border-foreground/30 rounded-full flex justify-center\"\n        >\n          <motion.div\n            animate={{ y: [0, 12, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n            className=\"w-1 h-3 bg-foreground/50 rounded-full mt-2\"\n          />\n        </motion.div>\n      </motion.div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,OAAO;IACX,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,eAAe;gBACf,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAO,WAAU;;0BAE3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;wBAA8F,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;kCAC3I,8OAAC;wBAAI,WAAU;wBAAmI,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;;;;;;;0BAGlL,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;sCAC5C,cAAA,8OAAC;gCAAK,WAAU;0CAAqF;;;;;;;;;;;sCAMvG,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;sCAEV,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAIlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;sCACX;;;;;;sCAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCACX;;;;;;sCAMD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,MAAK;oCACL,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;;wCACX;sDAEC,8OAAC,gNAAA,CAAA,YAAS;4CAAC,MAAM;;;;;;;;;;;;8CAGnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,MAAK;oCACL,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;oCACV,QAAQ;;wCACT;sDAEC,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;;;;;;;;;;;;;sCAKpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,YAAY;wCAAE,OAAO;wCAAK,GAAG,CAAC;oCAAE;oCAChC,UAAU;wCAAE,OAAO;oCAAI;oCACvB,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;;;;;;8CAGhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,YAAY;wCAAE,OAAO;wCAAK,GAAG,CAAC;oCAAE;oCAChC,UAAU;wCAAE,OAAO;oCAAI;oCACvB,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;;;;;;8CAGlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,MAAK;oCACL,YAAY;wCAAE,OAAO;wCAAK,GAAG,CAAC;oCAAE;oCAChC,UAAU;wCAAE,OAAO;oCAAI;oCACvB,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;gBACxC,WAAU;0BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;oBAC5C,WAAU;8BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBAAC;wBACzB,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;wBAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMtB;uCAEe", "debugId": null}}, {"offset": {"line": 625, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/port/src/components/About.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Code, Palette, Lightbulb, Users } from 'lucide-react';\n\nconst About = () => {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        delayChildren: 0.3,\n        staggerChildren: 0.2,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        duration: 0.5,\n      },\n    },\n  };\n\n  const features = [\n    {\n      icon: <Code size={32} />,\n      title: 'Clean Code',\n      description: 'Writing maintainable, scalable, and efficient code following best practices and modern standards.',\n    },\n    {\n      icon: <Palette size={32} />,\n      title: 'Creative Design',\n      description: 'Crafting visually appealing and user-friendly interfaces that enhance user experience.',\n    },\n    {\n      icon: <Lightbulb size={32} />,\n      title: 'Problem Solving',\n      description: 'Analyzing complex problems and developing innovative solutions that meet business needs.',\n    },\n    {\n      icon: <Users size={32} />,\n      title: 'Collaboration',\n      description: 'Working effectively with cross-functional teams to deliver exceptional digital products.',\n    },\n  ];\n\n  return (\n    <section id=\"about\" className=\"py-20 relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 -z-10\">\n        <div className=\"absolute top-1/3 right-1/4 w-64 h-64 bg-secondary/10 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-1/3 left-1/4 w-80 h-80 bg-accent/10 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n          className=\"max-w-6xl mx-auto\"\n        >\n          {/* Section Header */}\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\n              About <span className=\"gradient-text\">Me</span>\n            </h2>\n            <p className=\"text-xl text-foreground/70 max-w-3xl mx-auto\">\n              I'm a passionate web developer and UI/UX designer with a love for creating \n              digital experiences that are both beautiful and functional.\n            </p>\n          </motion.div>\n\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n            {/* Left Column - Text Content */}\n            <motion.div variants={itemVariants} className=\"space-y-6\">\n              <div className=\"space-y-4\">\n                <h3 className=\"text-2xl font-semibold text-foreground\">\n                  Crafting Digital Experiences\n                </h3>\n                <p className=\"text-foreground/80 leading-relaxed\">\n                  With a strong foundation in both development and design, I bridge the gap \n                  between technical implementation and user experience. My journey in web \n                  development started with a curiosity about how things work, and has evolved \n                  into a passion for creating meaningful digital solutions.\n                </p>\n                <p className=\"text-foreground/80 leading-relaxed\">\n                  I specialize in modern web technologies including React, Next.js, and \n                  TypeScript, while also having a keen eye for design principles, user \n                  psychology, and accessibility. I believe that great products are born \n                  from the intersection of beautiful design and robust functionality.\n                </p>\n              </div>\n\n              {/* Stats */}\n              <div className=\"grid grid-cols-2 gap-6 pt-6\">\n                <div className=\"text-center\">\n                  <div className=\"text-3xl font-bold gradient-text\">50+</div>\n                  <div className=\"text-foreground/70\">Projects Completed</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-3xl font-bold gradient-text\">3+</div>\n                  <div className=\"text-foreground/70\">Years Experience</div>\n                </div>\n              </div>\n            </motion.div>\n\n            {/* Right Column - Features Grid */}\n            <motion.div variants={itemVariants} className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\n              {features.map((feature, index) => (\n                <motion.div\n                  key={feature.title}\n                  variants={itemVariants}\n                  whileHover={{ scale: 1.05, y: -5 }}\n                  className=\"p-6 glass rounded-2xl hover:bg-foreground/5 transition-all duration-300\"\n                >\n                  <div className=\"text-primary mb-4\">{feature.icon}</div>\n                  <h4 className=\"text-lg font-semibold mb-2 text-foreground\">\n                    {feature.title}\n                  </h4>\n                  <p className=\"text-foreground/70 text-sm leading-relaxed\">\n                    {feature.description}\n                  </p>\n                </motion.div>\n              ))}\n            </motion.div>\n          </div>\n\n          {/* Philosophy */}\n          <motion.div\n            variants={itemVariants}\n            className=\"mt-16 text-center\"\n          >\n            <div className=\"glass p-8 rounded-3xl max-w-4xl mx-auto\">\n              <h3 className=\"text-2xl font-semibold mb-4 gradient-text\">My Philosophy</h3>\n              <p className=\"text-lg text-foreground/80 leading-relaxed\">\n                \"Good design is not just what it looks like and feels like. Good design is how it works.\" \n                I believe in creating solutions that are not only visually appealing but also intuitive, \n                accessible, and performant. Every line of code and every design decision should serve \n                the user's needs and enhance their experience.\n              </p>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default About;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,QAAQ;IACZ,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,eAAe;gBACf,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,MAAM,WAAW;QACf;YACE,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;YAClB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,wMAAA,CAAA,UAAO;gBAAC,MAAM;;;;;;YACrB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,4MAAA,CAAA,YAAS;gBAAC,MAAM;;;;;;YACvB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,MAAM;;;;;;YACnB,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAQ,WAAU;;0BAE5B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAI;oBACpC,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,8OAAC;oCAAG,WAAU;;wCAAsC;sDAC5C,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;8CAExC,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;;;;;;;sCAM9D,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,UAAU;oCAAc,WAAU;;sDAC5C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DAGvD,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAMlD,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;;;;;;;sDASpD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAmC;;;;;;sEAClD,8OAAC;4DAAI,WAAU;sEAAqB;;;;;;;;;;;;8DAEtC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAmC;;;;;;sEAClD,8OAAC;4DAAI,WAAU;sEAAqB;;;;;;;;;;;;;;;;;;;;;;;;8CAM1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,UAAU;oCAAc,WAAU;8CAC3C,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,UAAU;4CACV,YAAY;gDAAE,OAAO;gDAAM,GAAG,CAAC;4CAAE;4CACjC,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DAAqB,QAAQ,IAAI;;;;;;8DAChD,8OAAC;oDAAG,WAAU;8DACX,QAAQ,KAAK;;;;;;8DAEhB,8OAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;;2CAVjB,QAAQ,KAAK;;;;;;;;;;;;;;;;sCAkB1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxE;uCAEe", "debugId": null}}, {"offset": {"line": 1000, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/port/src/components/Skills.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\nconst Skills = () => {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        delayChildren: 0.3,\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        duration: 0.5,\n      },\n    },\n  };\n\n  const skillCategories = [\n    {\n      title: 'Frontend Development',\n      skills: [\n        { name: 'React', level: 90 },\n        { name: 'Next.js', level: 85 },\n        { name: 'TypeScript', level: 80 },\n        { name: 'JavaScript', level: 95 },\n        { name: 'HTML/CSS', level: 95 },\n        { name: 'Tailwind CSS', level: 90 },\n      ],\n    },\n    {\n      title: 'Backend Development',\n      skills: [\n        { name: 'Node.js', level: 80 },\n        { name: 'Express.js', level: 75 },\n        { name: 'MongoDB', level: 70 },\n        { name: 'PostgreSQL', level: 65 },\n        { name: 'REST APIs', level: 85 },\n        { name: 'GraphQL', level: 60 },\n      ],\n    },\n    {\n      title: 'Design & Tools',\n      skills: [\n        { name: 'Figma', level: 90 },\n        { name: 'Adobe XD', level: 85 },\n        { name: 'Photoshop', level: 80 },\n        { name: 'Illustrator', level: 75 },\n        { name: 'Framer', level: 70 },\n        { name: 'Sketch', level: 65 },\n      ],\n    },\n    {\n      title: 'Other Technologies',\n      skills: [\n        { name: 'Git/GitHub', level: 90 },\n        { name: 'Docker', level: 70 },\n        { name: 'AWS', level: 65 },\n        { name: 'Vercel', level: 85 },\n        { name: 'Firebase', level: 75 },\n        { name: 'Webpack', level: 70 },\n      ],\n    },\n  ];\n\n  return (\n    <section id=\"skills\" className=\"py-20 relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 -z-10\">\n        <div className=\"absolute top-1/4 left-1/3 w-72 h-72 bg-primary/10 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-1/4 right-1/3 w-96 h-96 bg-accent/10 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n          className=\"max-w-6xl mx-auto\"\n        >\n          {/* Section Header */}\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\n              My <span className=\"gradient-text\">Skills</span>\n            </h2>\n            <p className=\"text-xl text-foreground/70 max-w-3xl mx-auto\">\n              A comprehensive toolkit of technologies and design skills I use to bring ideas to life.\n            </p>\n          </motion.div>\n\n          {/* Skills Grid */}\n          <div className=\"grid md:grid-cols-2 gap-8\">\n            {skillCategories.map((category, categoryIndex) => (\n              <motion.div\n                key={category.title}\n                variants={itemVariants}\n                className=\"glass p-8 rounded-3xl hover:bg-foreground/5 transition-all duration-300\"\n              >\n                <h3 className=\"text-2xl font-semibold mb-6 gradient-text\">\n                  {category.title}\n                </h3>\n                \n                <div className=\"space-y-4\">\n                  {category.skills.map((skill, skillIndex) => (\n                    <div key={skill.name} className=\"space-y-2\">\n                      <div className=\"flex justify-between items-center\">\n                        <span className=\"text-foreground font-medium\">{skill.name}</span>\n                        <span className=\"text-foreground/70 text-sm\">{skill.level}%</span>\n                      </div>\n                      \n                      <div className=\"w-full bg-foreground/10 rounded-full h-2 overflow-hidden\">\n                        <motion.div\n                          initial={{ width: 0 }}\n                          whileInView={{ width: `${skill.level}%` }}\n                          transition={{ \n                            duration: 1, \n                            delay: categoryIndex * 0.2 + skillIndex * 0.1,\n                            ease: \"easeOut\"\n                          }}\n                          viewport={{ once: true }}\n                          className=\"h-full bg-gradient-to-r from-primary to-secondary rounded-full\"\n                        />\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </motion.div>\n            ))}\n          </div>\n\n          {/* Additional Skills Tags */}\n          <motion.div variants={itemVariants} className=\"mt-16 text-center\">\n            <h3 className=\"text-2xl font-semibold mb-8 text-foreground\">\n              Technologies I Work With\n            </h3>\n            \n            <div className=\"flex flex-wrap justify-center gap-3 max-w-4xl mx-auto\">\n              {[\n                'React', 'Next.js', 'TypeScript', 'JavaScript', 'Node.js', 'Express.js',\n                'MongoDB', 'PostgreSQL', 'Tailwind CSS', 'Framer Motion', 'Figma',\n                'Adobe Creative Suite', 'Git', 'Docker', 'AWS', 'Vercel', 'Firebase',\n                'REST APIs', 'GraphQL', 'Responsive Design', 'UI/UX Design', 'Prototyping'\n              ].map((tech, index) => (\n                <motion.span\n                  key={tech}\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  whileInView={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.3, delay: index * 0.05 }}\n                  whileHover={{ scale: 1.1, y: -2 }}\n                  viewport={{ once: true }}\n                  className=\"px-4 py-2 glass rounded-full text-sm font-medium text-foreground hover:bg-primary/20 transition-all duration-200 cursor-default\"\n                >\n                  {tech}\n                </motion.span>\n              ))}\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Skills;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,SAAS;IACb,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,eAAe;gBACf,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB;YACE,OAAO;YACP,QAAQ;gBACN;oBAAE,MAAM;oBAAS,OAAO;gBAAG;gBAC3B;oBAAE,MAAM;oBAAW,OAAO;gBAAG;gBAC7B;oBAAE,MAAM;oBAAc,OAAO;gBAAG;gBAChC;oBAAE,MAAM;oBAAc,OAAO;gBAAG;gBAChC;oBAAE,MAAM;oBAAY,OAAO;gBAAG;gBAC9B;oBAAE,MAAM;oBAAgB,OAAO;gBAAG;aACnC;QACH;QACA;YACE,OAAO;YACP,QAAQ;gBACN;oBAAE,MAAM;oBAAW,OAAO;gBAAG;gBAC7B;oBAAE,MAAM;oBAAc,OAAO;gBAAG;gBAChC;oBAAE,MAAM;oBAAW,OAAO;gBAAG;gBAC7B;oBAAE,MAAM;oBAAc,OAAO;gBAAG;gBAChC;oBAAE,MAAM;oBAAa,OAAO;gBAAG;gBAC/B;oBAAE,MAAM;oBAAW,OAAO;gBAAG;aAC9B;QACH;QACA;YACE,OAAO;YACP,QAAQ;gBACN;oBAAE,MAAM;oBAAS,OAAO;gBAAG;gBAC3B;oBAAE,MAAM;oBAAY,OAAO;gBAAG;gBAC9B;oBAAE,MAAM;oBAAa,OAAO;gBAAG;gBAC/B;oBAAE,MAAM;oBAAe,OAAO;gBAAG;gBACjC;oBAAE,MAAM;oBAAU,OAAO;gBAAG;gBAC5B;oBAAE,MAAM;oBAAU,OAAO;gBAAG;aAC7B;QACH;QACA;YACE,OAAO;YACP,QAAQ;gBACN;oBAAE,MAAM;oBAAc,OAAO;gBAAG;gBAChC;oBAAE,MAAM;oBAAU,OAAO;gBAAG;gBAC5B;oBAAE,MAAM;oBAAO,OAAO;gBAAG;gBACzB;oBAAE,MAAM;oBAAU,OAAO;gBAAG;gBAC5B;oBAAE,MAAM;oBAAY,OAAO;gBAAG;gBAC9B;oBAAE,MAAM;oBAAW,OAAO;gBAAG;aAC9B;QACH;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAS,WAAU;;0BAE7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAI;oBACpC,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,8OAAC;oCAAG,WAAU;;wCAAsC;sDAC/C,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;8CAErC,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;;;;;;;sCAM9D,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,UAAU,8BAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,UAAU;oCACV,WAAU;;sDAEV,8OAAC;4CAAG,WAAU;sDACX,SAAS,KAAK;;;;;;sDAGjB,8OAAC;4CAAI,WAAU;sDACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,2BAC3B,8OAAC;oDAAqB,WAAU;;sEAC9B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAA+B,MAAM,IAAI;;;;;;8EACzD,8OAAC;oEAAK,WAAU;;wEAA8B,MAAM,KAAK;wEAAC;;;;;;;;;;;;;sEAG5D,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,SAAS;oEAAE,OAAO;gEAAE;gEACpB,aAAa;oEAAE,OAAO,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC;gEAAC;gEACxC,YAAY;oEACV,UAAU;oEACV,OAAO,gBAAgB,MAAM,aAAa;oEAC1C,MAAM;gEACR;gEACA,UAAU;oEAAE,MAAM;gEAAK;gEACvB,WAAU;;;;;;;;;;;;mDAhBN,MAAM,IAAI;;;;;;;;;;;mCAVnB,SAAS,KAAK;;;;;;;;;;sCAqCzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,8OAAC;oCAAG,WAAU;8CAA8C;;;;;;8CAI5D,8OAAC;oCAAI,WAAU;8CACZ;wCACC;wCAAS;wCAAW;wCAAc;wCAAc;wCAAW;wCAC3D;wCAAW;wCAAc;wCAAgB;wCAAiB;wCAC1D;wCAAwB;wCAAO;wCAAU;wCAAO;wCAAU;wCAC1D;wCAAa;wCAAW;wCAAqB;wCAAgB;qCAC9D,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4CAEV,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,aAAa;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CACpC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAK;4CACjD,YAAY;gDAAE,OAAO;gDAAK,GAAG,CAAC;4CAAE;4CAChC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;sDAET;2CARI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBvB;uCAEe", "debugId": null}}, {"offset": {"line": 1424, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/port/src/components/Projects.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ExternalLink, Github, Eye } from 'lucide-react';\n\nconst Projects = () => {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        delayChildren: 0.3,\n        staggerChildren: 0.2,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        duration: 0.5,\n      },\n    },\n  };\n\n  const projects = [\n    {\n      title: 'E-Commerce Platform',\n      description: 'A full-stack e-commerce solution with modern UI, payment integration, and admin dashboard. Built with Next.js, TypeScript, and Stripe.',\n      image: '/placeholder-project.svg',\n      technologies: ['Next.js', 'TypeScript', 'Tailwind CSS', 'Stripe', 'MongoDB'],\n      liveUrl: 'https://example.com',\n      githubUrl: 'https://github.com/bhupenderyadav/ecommerce',\n      featured: true,\n    },\n    {\n      title: 'Task Management App',\n      description: 'A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.',\n      image: '/placeholder-project.svg',\n      technologies: ['React', 'Node.js', 'Socket.io', 'MongoDB', 'Material-UI'],\n      liveUrl: 'https://example.com',\n      githubUrl: 'https://github.com/bhupenderyadav/taskmanager',\n      featured: true,\n    },\n    {\n      title: 'Weather Dashboard',\n      description: 'A beautiful weather dashboard with location-based forecasts, interactive maps, and detailed weather analytics.',\n      image: '/placeholder-project.svg',\n      technologies: ['React', 'OpenWeather API', 'Chart.js', 'CSS3'],\n      liveUrl: 'https://example.com',\n      githubUrl: 'https://github.com/bhupenderyadav/weather-dashboard',\n      featured: false,\n    },\n    {\n      title: 'Portfolio Website',\n      description: 'A responsive portfolio website showcasing modern design principles and smooth animations. Built with Next.js and Framer Motion.',\n      image: '/placeholder-project.svg',\n      technologies: ['Next.js', 'Framer Motion', 'Tailwind CSS', 'TypeScript'],\n      liveUrl: 'https://example.com',\n      githubUrl: 'https://github.com/bhupenderyadav/portfolio',\n      featured: false,\n    },\n    {\n      title: 'Social Media Dashboard',\n      description: 'A comprehensive social media analytics dashboard with data visualization and performance tracking across multiple platforms.',\n      image: '/placeholder-project.svg',\n      technologies: ['Vue.js', 'D3.js', 'Express.js', 'PostgreSQL'],\n      liveUrl: 'https://example.com',\n      githubUrl: 'https://github.com/bhupenderyadav/social-dashboard',\n      featured: false,\n    },\n    {\n      title: 'Learning Management System',\n      description: 'An educational platform with course management, video streaming, progress tracking, and interactive quizzes.',\n      image: '/placeholder-project.svg',\n      technologies: ['React', 'Node.js', 'MongoDB', 'AWS S3', 'JWT'],\n      liveUrl: 'https://example.com',\n      githubUrl: 'https://github.com/bhupenderyadav/lms',\n      featured: false,\n    },\n  ];\n\n  return (\n    <section id=\"projects\" className=\"py-20 relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 -z-10\">\n        <div className=\"absolute top-1/4 right-1/4 w-80 h-80 bg-secondary/10 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-1/4 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n          className=\"max-w-7xl mx-auto\"\n        >\n          {/* Section Header */}\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\n              Featured <span className=\"gradient-text\">Projects</span>\n            </h2>\n            <p className=\"text-xl text-foreground/70 max-w-3xl mx-auto\">\n              A showcase of my recent work, demonstrating my skills in web development and UI/UX design.\n            </p>\n          </motion.div>\n\n          {/* Featured Projects */}\n          <div className=\"space-y-16 mb-16\">\n            {projects.filter(project => project.featured).map((project, index) => (\n              <motion.div\n                key={project.title}\n                variants={itemVariants}\n                className={`grid lg:grid-cols-2 gap-12 items-center ${\n                  index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''\n                }`}\n              >\n                {/* Project Image */}\n                <motion.div\n                  whileHover={{ scale: 1.02 }}\n                  className={`relative group ${index % 2 === 1 ? 'lg:col-start-2' : ''}`}\n                >\n                  <div className=\"glass rounded-3xl overflow-hidden\">\n                    <img\n                      src={project.image}\n                      alt={project.title}\n                      className=\"w-full h-64 md:h-80 object-cover transition-transform duration-300 group-hover:scale-105\"\n                    />\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-6\">\n                      <div className=\"flex space-x-4\">\n                        <motion.a\n                          href={project.liveUrl}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          whileHover={{ scale: 1.1 }}\n                          whileTap={{ scale: 0.9 }}\n                          className=\"p-3 bg-white/20 backdrop-blur-sm rounded-full hover:bg-white/30 transition-colors\"\n                        >\n                          <ExternalLink size={20} className=\"text-white\" />\n                        </motion.a>\n                        <motion.a\n                          href={project.githubUrl}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          whileHover={{ scale: 1.1 }}\n                          whileTap={{ scale: 0.9 }}\n                          className=\"p-3 bg-white/20 backdrop-blur-sm rounded-full hover:bg-white/30 transition-colors\"\n                        >\n                          <Github size={20} className=\"text-white\" />\n                        </motion.a>\n                      </div>\n                    </div>\n                  </div>\n                </motion.div>\n\n                {/* Project Info */}\n                <div className={`space-y-6 ${index % 2 === 1 ? 'lg:col-start-1 lg:row-start-1' : ''}`}>\n                  <div>\n                    <h3 className=\"text-3xl font-bold mb-4 text-foreground\">\n                      {project.title}\n                    </h3>\n                    <p className=\"text-foreground/80 text-lg leading-relaxed\">\n                      {project.description}\n                    </p>\n                  </div>\n\n                  <div className=\"flex flex-wrap gap-2\">\n                    {project.technologies.map((tech) => (\n                      <span\n                        key={tech}\n                        className=\"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium\"\n                      >\n                        {tech}\n                      </span>\n                    ))}\n                  </div>\n\n                  <div className=\"flex space-x-4\">\n                    <motion.a\n                      href={project.liveUrl}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      className=\"flex items-center gap-2 px-6 py-3 bg-primary text-white rounded-full font-medium hover:bg-primary/90 transition-colors\"\n                    >\n                      <Eye size={18} />\n                      Live Demo\n                    </motion.a>\n                    <motion.a\n                      href={project.githubUrl}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      className=\"flex items-center gap-2 px-6 py-3 glass text-foreground rounded-full font-medium hover:bg-foreground/10 transition-colors\"\n                    >\n                      <Github size={18} />\n                      View Code\n                    </motion.a>\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n\n          {/* Other Projects Grid */}\n          <motion.div variants={itemVariants}>\n            <h3 className=\"text-3xl font-bold text-center mb-12 text-foreground\">\n              Other Notable Projects\n            </h3>\n            \n            <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {projects.filter(project => !project.featured).map((project) => (\n                <motion.div\n                  key={project.title}\n                  variants={itemVariants}\n                  whileHover={{ y: -10 }}\n                  className=\"glass p-6 rounded-3xl hover:bg-foreground/5 transition-all duration-300\"\n                >\n                  <div className=\"space-y-4\">\n                    <div className=\"flex justify-between items-start\">\n                      <h4 className=\"text-xl font-semibold text-foreground\">\n                        {project.title}\n                      </h4>\n                      <div className=\"flex space-x-2\">\n                        <motion.a\n                          href={project.liveUrl}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          whileHover={{ scale: 1.1 }}\n                          className=\"p-2 hover:bg-primary/20 rounded-lg transition-colors\"\n                        >\n                          <ExternalLink size={16} />\n                        </motion.a>\n                        <motion.a\n                          href={project.githubUrl}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          whileHover={{ scale: 1.1 }}\n                          className=\"p-2 hover:bg-primary/20 rounded-lg transition-colors\"\n                        >\n                          <Github size={16} />\n                        </motion.a>\n                      </div>\n                    </div>\n                    \n                    <p className=\"text-foreground/70 text-sm leading-relaxed\">\n                      {project.description}\n                    </p>\n                    \n                    <div className=\"flex flex-wrap gap-2\">\n                      {project.technologies.slice(0, 3).map((tech) => (\n                        <span\n                          key={tech}\n                          className=\"px-2 py-1 bg-primary/10 text-primary rounded text-xs font-medium\"\n                        >\n                          {tech}\n                        </span>\n                      ))}\n                      {project.technologies.length > 3 && (\n                        <span className=\"px-2 py-1 bg-foreground/10 text-foreground/70 rounded text-xs\">\n                          +{project.technologies.length - 3} more\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Projects;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,WAAW;IACf,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,eAAe;gBACf,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,MAAM,WAAW;QACf;YACE,OAAO;YACP,aAAa;YACb,OAAO;YACP,cAAc;gBAAC;gBAAW;gBAAc;gBAAgB;gBAAU;aAAU;YAC5E,SAAS;YACT,WAAW;YACX,UAAU;QACZ;QACA;YACE,OAAO;YACP,aAAa;YACb,OAAO;YACP,cAAc;gBAAC;gBAAS;gBAAW;gBAAa;gBAAW;aAAc;YACzE,SAAS;YACT,WAAW;YACX,UAAU;QACZ;QACA;YACE,OAAO;YACP,aAAa;YACb,OAAO;YACP,cAAc;gBAAC;gBAAS;gBAAmB;gBAAY;aAAO;YAC9D,SAAS;YACT,WAAW;YACX,UAAU;QACZ;QACA;YACE,OAAO;YACP,aAAa;YACb,OAAO;YACP,cAAc;gBAAC;gBAAW;gBAAiB;gBAAgB;aAAa;YACxE,SAAS;YACT,WAAW;YACX,UAAU;QACZ;QACA;YACE,OAAO;YACP,aAAa;YACb,OAAO;YACP,cAAc;gBAAC;gBAAU;gBAAS;gBAAc;aAAa;YAC7D,SAAS;YACT,WAAW;YACX,UAAU;QACZ;QACA;YACE,OAAO;YACP,aAAa;YACb,OAAO;YACP,cAAc;gBAAC;gBAAS;gBAAW;gBAAW;gBAAU;aAAM;YAC9D,SAAS;YACT,WAAW;YACX,UAAU;QACZ;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;;0BAE/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAI;oBACpC,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,8OAAC;oCAAG,WAAU;;wCAAsC;sDACzC,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;8CAE3C,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;;;;;;;sCAM9D,8OAAC;4BAAI,WAAU;sCACZ,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,EAAE,GAAG,CAAC,CAAC,SAAS,sBAC1D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,UAAU;oCACV,WAAW,CAAC,wCAAwC,EAClD,QAAQ,MAAM,IAAI,2BAA2B,IAC7C;;sDAGF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,WAAW,CAAC,eAAe,EAAE,QAAQ,MAAM,IAAI,mBAAmB,IAAI;sDAEtE,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,KAAK,QAAQ,KAAK;wDAClB,KAAK,QAAQ,KAAK;wDAClB,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oEACP,MAAM,QAAQ,OAAO;oEACrB,QAAO;oEACP,KAAI;oEACJ,YAAY;wEAAE,OAAO;oEAAI;oEACzB,UAAU;wEAAE,OAAO;oEAAI;oEACvB,WAAU;8EAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;wEAAC,MAAM;wEAAI,WAAU;;;;;;;;;;;8EAEpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oEACP,MAAM,QAAQ,SAAS;oEACvB,QAAO;oEACP,KAAI;oEACJ,YAAY;wEAAE,OAAO;oEAAI;oEACzB,UAAU;wEAAE,OAAO;oEAAI;oEACvB,WAAU;8EAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;wEAAC,MAAM;wEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQtC,8OAAC;4CAAI,WAAW,CAAC,UAAU,EAAE,QAAQ,MAAM,IAAI,kCAAkC,IAAI;;8DACnF,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,QAAQ,KAAK;;;;;;sEAEhB,8OAAC;4DAAE,WAAU;sEACV,QAAQ,WAAW;;;;;;;;;;;;8DAIxB,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC;4DAEC,WAAU;sEAET;2DAHI;;;;;;;;;;8DAQX,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4DACP,MAAM,QAAQ,OAAO;4DACrB,QAAO;4DACP,KAAI;4DACJ,YAAY;gEAAE,OAAO;4DAAK;4DAC1B,UAAU;gEAAE,OAAO;4DAAK;4DACxB,WAAU;;8EAEV,8OAAC,gMAAA,CAAA,MAAG;oEAAC,MAAM;;;;;;gEAAM;;;;;;;sEAGnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4DACP,MAAM,QAAQ,SAAS;4DACvB,QAAO;4DACP,KAAI;4DACJ,YAAY;gEAAE,OAAO;4DAAK;4DAC1B,UAAU;gEAAE,OAAO;4DAAK;4DACxB,WAAU;;8EAEV,8OAAC,sMAAA,CAAA,SAAM;oEAAC,MAAM;;;;;;gEAAM;;;;;;;;;;;;;;;;;;;;mCAtFrB,QAAQ,KAAK;;;;;;;;;;sCAgGxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;;8CACpB,8OAAC;oCAAG,WAAU;8CAAuD;;;;;;8CAIrE,8OAAC;oCAAI,WAAU;8CACZ,SAAS,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,QAAQ,EAAE,GAAG,CAAC,CAAC,wBAClD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,UAAU;4CACV,YAAY;gDAAE,GAAG,CAAC;4CAAG;4CACrB,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EACX,QAAQ,KAAK;;;;;;0EAEhB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wEACP,MAAM,QAAQ,OAAO;wEACrB,QAAO;wEACP,KAAI;wEACJ,YAAY;4EAAE,OAAO;wEAAI;wEACzB,WAAU;kFAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;4EAAC,MAAM;;;;;;;;;;;kFAEtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wEACP,MAAM,QAAQ,SAAS;wEACvB,QAAO;wEACP,KAAI;wEACJ,YAAY;4EAAE,OAAO;wEAAI;wEACzB,WAAU;kFAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;4EAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;kEAKpB,8OAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;kEAGtB,8OAAC;wDAAI,WAAU;;4DACZ,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACrC,8OAAC;oEAEC,WAAU;8EAET;mEAHI;;;;;4DAMR,QAAQ,YAAY,CAAC,MAAM,GAAG,mBAC7B,8OAAC;gEAAK,WAAU;;oEAAgE;oEAC5E,QAAQ,YAAY,CAAC,MAAM,GAAG;oEAAE;;;;;;;;;;;;;;;;;;;2CA/CrC,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DpC;uCAEe", "debugId": null}}, {"offset": {"line": 2016, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/port/src/components/Experience.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Calendar, MapPin, Briefcase } from 'lucide-react';\n\nconst Experience = () => {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        delayChildren: 0.3,\n        staggerChildren: 0.2,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        duration: 0.5,\n      },\n    },\n  };\n\n  const experiences = [\n    {\n      title: 'Senior Frontend Developer',\n      company: 'TechCorp Solutions',\n      location: 'Remote',\n      period: '2022 - Present',\n      type: 'Full-time',\n      description: 'Leading frontend development for enterprise applications, mentoring junior developers, and implementing modern React architectures.',\n      achievements: [\n        'Improved application performance by 40% through code optimization',\n        'Led a team of 5 developers in rebuilding the main product dashboard',\n        'Implemented design system that reduced development time by 30%',\n        'Introduced TypeScript and modern testing practices'\n      ],\n      technologies: ['React', 'TypeScript', 'Next.js', 'Tailwind CSS', 'GraphQL']\n    },\n    {\n      title: 'UI/UX Designer & Frontend Developer',\n      company: 'Digital Innovations Inc.',\n      location: 'New York, NY',\n      period: '2021 - 2022',\n      type: 'Full-time',\n      description: 'Designed and developed user interfaces for web applications, conducted user research, and collaborated with product teams.',\n      achievements: [\n        'Designed and developed 15+ responsive web applications',\n        'Increased user engagement by 25% through UX improvements',\n        'Created comprehensive design system and component library',\n        'Conducted user testing sessions and implemented feedback'\n      ],\n      technologies: ['React', 'JavaScript', 'Figma', 'Adobe XD', 'SCSS', 'Node.js']\n    },\n    {\n      title: 'Frontend Developer',\n      company: 'StartupXYZ',\n      location: 'San Francisco, CA',\n      period: '2020 - 2021',\n      type: 'Full-time',\n      description: 'Developed responsive web applications and collaborated with designers to implement pixel-perfect user interfaces.',\n      achievements: [\n        'Built and maintained 10+ client websites',\n        'Implemented responsive design principles across all projects',\n        'Collaborated with backend team to integrate REST APIs',\n        'Optimized websites for SEO and performance'\n      ],\n      technologies: ['HTML', 'CSS', 'JavaScript', 'React', 'Bootstrap', 'WordPress']\n    },\n    {\n      title: 'Junior Web Developer',\n      company: 'WebDev Agency',\n      location: 'Austin, TX',\n      period: '2019 - 2020',\n      type: 'Full-time',\n      description: 'Started my professional journey developing websites and learning modern web development practices.',\n      achievements: [\n        'Completed 20+ client projects successfully',\n        'Learned modern JavaScript frameworks and tools',\n        'Contributed to team coding standards and best practices',\n        'Participated in code reviews and team meetings'\n      ],\n      technologies: ['HTML', 'CSS', 'JavaScript', 'jQuery', 'PHP', 'MySQL']\n    }\n  ];\n\n  return (\n    <section id=\"experience\" className=\"py-20 relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 -z-10\">\n        <div className=\"absolute top-1/3 left-1/4 w-72 h-72 bg-accent/10 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-1/3 right-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n          className=\"max-w-4xl mx-auto\"\n        >\n          {/* Section Header */}\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\n              Work <span className=\"gradient-text\">Experience</span>\n            </h2>\n            <p className=\"text-xl text-foreground/70 max-w-3xl mx-auto\">\n              My professional journey in web development and design, building expertise through diverse projects and teams.\n            </p>\n          </motion.div>\n\n          {/* Timeline */}\n          <div className=\"relative\">\n            {/* Timeline Line */}\n            <div className=\"absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary via-secondary to-accent\"></div>\n\n            {/* Experience Items */}\n            <div className=\"space-y-12\">\n              {experiences.map((experience, index) => (\n                <motion.div\n                  key={experience.title + experience.company}\n                  variants={itemVariants}\n                  className=\"relative pl-20\"\n                >\n                  {/* Timeline Dot */}\n                  <motion.div\n                    initial={{ scale: 0 }}\n                    whileInView={{ scale: 1 }}\n                    transition={{ duration: 0.5, delay: index * 0.1 }}\n                    viewport={{ once: true }}\n                    className=\"absolute left-6 top-6 w-4 h-4 bg-primary rounded-full border-4 border-background shadow-lg\"\n                  />\n\n                  {/* Experience Card */}\n                  <motion.div\n                    whileHover={{ scale: 1.02, y: -5 }}\n                    className=\"glass p-8 rounded-3xl hover:bg-foreground/5 transition-all duration-300\"\n                  >\n                    {/* Header */}\n                    <div className=\"mb-6\">\n                      <div className=\"flex flex-wrap items-center gap-4 mb-2\">\n                        <h3 className=\"text-2xl font-bold text-foreground\">\n                          {experience.title}\n                        </h3>\n                        <span className=\"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium\">\n                          {experience.type}\n                        </span>\n                      </div>\n                      \n                      <div className=\"flex flex-wrap items-center gap-4 text-foreground/70 mb-4\">\n                        <div className=\"flex items-center gap-2\">\n                          <Briefcase size={16} />\n                          <span className=\"font-semibold text-foreground\">{experience.company}</span>\n                        </div>\n                        <div className=\"flex items-center gap-2\">\n                          <MapPin size={16} />\n                          <span>{experience.location}</span>\n                        </div>\n                        <div className=\"flex items-center gap-2\">\n                          <Calendar size={16} />\n                          <span>{experience.period}</span>\n                        </div>\n                      </div>\n                      \n                      <p className=\"text-foreground/80 leading-relaxed\">\n                        {experience.description}\n                      </p>\n                    </div>\n\n                    {/* Achievements */}\n                    <div className=\"mb-6\">\n                      <h4 className=\"text-lg font-semibold mb-3 text-foreground\">\n                        Key Achievements:\n                      </h4>\n                      <ul className=\"space-y-2\">\n                        {experience.achievements.map((achievement, achievementIndex) => (\n                          <motion.li\n                            key={achievementIndex}\n                            initial={{ opacity: 0, x: -20 }}\n                            whileInView={{ opacity: 1, x: 0 }}\n                            transition={{ duration: 0.3, delay: achievementIndex * 0.1 }}\n                            viewport={{ once: true }}\n                            className=\"flex items-start gap-3 text-foreground/80\"\n                          >\n                            <div className=\"w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0\"></div>\n                            <span>{achievement}</span>\n                          </motion.li>\n                        ))}\n                      </ul>\n                    </div>\n\n                    {/* Technologies */}\n                    <div>\n                      <h4 className=\"text-lg font-semibold mb-3 text-foreground\">\n                        Technologies Used:\n                      </h4>\n                      <div className=\"flex flex-wrap gap-2\">\n                        {experience.technologies.map((tech) => (\n                          <span\n                            key={tech}\n                            className=\"px-3 py-1 bg-secondary/10 text-secondary rounded-full text-sm font-medium\"\n                          >\n                            {tech}\n                          </span>\n                        ))}\n                      </div>\n                    </div>\n                  </motion.div>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n\n          {/* Call to Action */}\n          <motion.div\n            variants={itemVariants}\n            className=\"text-center mt-16\"\n          >\n            <div className=\"glass p-8 rounded-3xl\">\n              <h3 className=\"text-2xl font-semibold mb-4 gradient-text\">\n                Let's Work Together\n              </h3>\n              <p className=\"text-foreground/80 mb-6 max-w-2xl mx-auto\">\n                I'm always interested in new opportunities and exciting projects. \n                Whether you're looking for a developer, designer, or both, let's discuss how we can create something amazing together.\n              </p>\n              <motion.a\n                href=\"#contact\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"inline-flex items-center gap-2 px-8 py-4 bg-primary text-white rounded-full font-medium hover:bg-primary/90 transition-colors duration-200\"\n              >\n                Get In Touch\n              </motion.a>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Experience;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,aAAa;IACjB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,eAAe;gBACf,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,MAAM,cAAc;QAClB;YACE,OAAO;YACP,SAAS;YACT,UAAU;YACV,QAAQ;YACR,MAAM;YACN,aAAa;YACb,cAAc;gBACZ;gBACA;gBACA;gBACA;aACD;YACD,cAAc;gBAAC;gBAAS;gBAAc;gBAAW;gBAAgB;aAAU;QAC7E;QACA;YACE,OAAO;YACP,SAAS;YACT,UAAU;YACV,QAAQ;YACR,MAAM;YACN,aAAa;YACb,cAAc;gBACZ;gBACA;gBACA;gBACA;aACD;YACD,cAAc;gBAAC;gBAAS;gBAAc;gBAAS;gBAAY;gBAAQ;aAAU;QAC/E;QACA;YACE,OAAO;YACP,SAAS;YACT,UAAU;YACV,QAAQ;YACR,MAAM;YACN,aAAa;YACb,cAAc;gBACZ;gBACA;gBACA;gBACA;aACD;YACD,cAAc;gBAAC;gBAAQ;gBAAO;gBAAc;gBAAS;gBAAa;aAAY;QAChF;QACA;YACE,OAAO;YACP,SAAS;YACT,UAAU;YACV,QAAQ;YACR,MAAM;YACN,aAAa;YACb,cAAc;gBACZ;gBACA;gBACA;gBACA;aACD;YACD,cAAc;gBAAC;gBAAQ;gBAAO;gBAAc;gBAAU;gBAAO;aAAQ;QACvE;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAa,WAAU;;0BAEjC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAI;oBACpC,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,8OAAC;oCAAG,WAAU;;wCAAsC;sDAC7C,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;8CAEvC,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;;;;;;;sCAM9D,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,UAAU;4CACV,WAAU;;8DAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,OAAO;oDAAE;oDACpB,aAAa;wDAAE,OAAO;oDAAE;oDACxB,YAAY;wDAAE,UAAU;wDAAK,OAAO,QAAQ;oDAAI;oDAChD,UAAU;wDAAE,MAAM;oDAAK;oDACvB,WAAU;;;;;;8DAIZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,YAAY;wDAAE,OAAO;wDAAM,GAAG,CAAC;oDAAE;oDACjC,WAAU;;sEAGV,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFACX,WAAW,KAAK;;;;;;sFAEnB,8OAAC;4EAAK,WAAU;sFACb,WAAW,IAAI;;;;;;;;;;;;8EAIpB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,4MAAA,CAAA,YAAS;oFAAC,MAAM;;;;;;8FACjB,8OAAC;oFAAK,WAAU;8FAAiC,WAAW,OAAO;;;;;;;;;;;;sFAErE,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0MAAA,CAAA,SAAM;oFAAC,MAAM;;;;;;8FACd,8OAAC;8FAAM,WAAW,QAAQ;;;;;;;;;;;;sFAE5B,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0MAAA,CAAA,WAAQ;oFAAC,MAAM;;;;;;8FAChB,8OAAC;8FAAM,WAAW,MAAM;;;;;;;;;;;;;;;;;;8EAI5B,8OAAC;oEAAE,WAAU;8EACV,WAAW,WAAW;;;;;;;;;;;;sEAK3B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAA6C;;;;;;8EAG3D,8OAAC;oEAAG,WAAU;8EACX,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,iCACzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4EAER,SAAS;gFAAE,SAAS;gFAAG,GAAG,CAAC;4EAAG;4EAC9B,aAAa;gFAAE,SAAS;gFAAG,GAAG;4EAAE;4EAChC,YAAY;gFAAE,UAAU;gFAAK,OAAO,mBAAmB;4EAAI;4EAC3D,UAAU;gFAAE,MAAM;4EAAK;4EACvB,WAAU;;8FAEV,8OAAC;oFAAI,WAAU;;;;;;8FACf,8OAAC;8FAAM;;;;;;;2EARF;;;;;;;;;;;;;;;;sEAeb,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA6C;;;;;;8EAG3D,8OAAC;oEAAI,WAAU;8EACZ,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,qBAC5B,8OAAC;4EAEC,WAAU;sFAET;2EAHI;;;;;;;;;;;;;;;;;;;;;;;2CA/EV,WAAW,KAAK,GAAG,WAAW,OAAO;;;;;;;;;;;;;;;;sCA8FlD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAG1D,8OAAC;wCAAE,WAAU;kDAA4C;;;;;;kDAIzD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,MAAK;wCACL,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 2571, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/port/src/components/Contact.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Mail, Phone, MapPin, Send, Github, Linkedin, Twitter } from 'lucide-react';\n\nconst Contact = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        delayChildren: 0.3,\n        staggerChildren: 0.2,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        duration: 0.5,\n      },\n    },\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    \n    // Simulate form submission\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    \n    // Reset form\n    setFormData({ name: '', email: '', subject: '', message: '' });\n    setIsSubmitting(false);\n    \n    // You would typically send the data to your backend here\n    alert('Thank you for your message! I\\'ll get back to you soon.');\n  };\n\n  const contactInfo = [\n    {\n      icon: <Mail size={24} />,\n      title: 'Email',\n      value: '<EMAIL>',\n      link: 'mailto:<EMAIL>'\n    },\n    {\n      icon: <Phone size={24} />,\n      title: 'Phone',\n      value: '+****************',\n      link: 'tel:+15551234567'\n    },\n    {\n      icon: <MapPin size={24} />,\n      title: 'Location',\n      value: 'New York, NY',\n      link: null\n    }\n  ];\n\n  const socialLinks = [\n    {\n      icon: <Github size={24} />,\n      name: 'GitHub',\n      url: 'https://github.com/bhupenderyadav'\n    },\n    {\n      icon: <Linkedin size={24} />,\n      name: 'LinkedIn',\n      url: 'https://linkedin.com/in/bhupenderyadav'\n    },\n    {\n      icon: <Twitter size={24} />,\n      name: 'Twitter',\n      url: 'https://twitter.com/bhupenderyadav'\n    }\n  ];\n\n  return (\n    <section id=\"contact\" className=\"py-20 relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 -z-10\">\n        <div className=\"absolute top-1/4 left-1/3 w-80 h-80 bg-primary/10 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-1/4 right-1/3 w-96 h-96 bg-secondary/10 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n          className=\"max-w-6xl mx-auto\"\n        >\n          {/* Section Header */}\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\n              Get In <span className=\"gradient-text\">Touch</span>\n            </h2>\n            <p className=\"text-xl text-foreground/70 max-w-3xl mx-auto\">\n              Have a project in mind or just want to chat? I'd love to hear from you. \n              Let's create something amazing together!\n            </p>\n          </motion.div>\n\n          <div className=\"grid lg:grid-cols-2 gap-12\">\n            {/* Contact Form */}\n            <motion.div variants={itemVariants}>\n              <div className=\"glass p-8 rounded-3xl\">\n                <h3 className=\"text-2xl font-semibold mb-6 text-foreground\">\n                  Send me a message\n                </h3>\n                \n                <form onSubmit={handleSubmit} className=\"space-y-6\">\n                  <div className=\"grid md:grid-cols-2 gap-6\">\n                    <div>\n                      <label htmlFor=\"name\" className=\"block text-sm font-medium text-foreground mb-2\">\n                        Name *\n                      </label>\n                      <input\n                        type=\"text\"\n                        id=\"name\"\n                        name=\"name\"\n                        value={formData.name}\n                        onChange={handleInputChange}\n                        required\n                        className=\"w-full px-4 py-3 bg-foreground/5 border border-foreground/20 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200\"\n                        placeholder=\"Your name\"\n                      />\n                    </div>\n                    \n                    <div>\n                      <label htmlFor=\"email\" className=\"block text-sm font-medium text-foreground mb-2\">\n                        Email *\n                      </label>\n                      <input\n                        type=\"email\"\n                        id=\"email\"\n                        name=\"email\"\n                        value={formData.email}\n                        onChange={handleInputChange}\n                        required\n                        className=\"w-full px-4 py-3 bg-foreground/5 border border-foreground/20 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200\"\n                        placeholder=\"<EMAIL>\"\n                      />\n                    </div>\n                  </div>\n                  \n                  <div>\n                    <label htmlFor=\"subject\" className=\"block text-sm font-medium text-foreground mb-2\">\n                      Subject *\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"subject\"\n                      name=\"subject\"\n                      value={formData.subject}\n                      onChange={handleInputChange}\n                      required\n                      className=\"w-full px-4 py-3 bg-foreground/5 border border-foreground/20 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200\"\n                      placeholder=\"What's this about?\"\n                    />\n                  </div>\n                  \n                  <div>\n                    <label htmlFor=\"message\" className=\"block text-sm font-medium text-foreground mb-2\">\n                      Message *\n                    </label>\n                    <textarea\n                      id=\"message\"\n                      name=\"message\"\n                      value={formData.message}\n                      onChange={handleInputChange}\n                      required\n                      rows={6}\n                      className=\"w-full px-4 py-3 bg-foreground/5 border border-foreground/20 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 resize-none\"\n                      placeholder=\"Tell me about your project or just say hello!\"\n                    />\n                  </div>\n                  \n                  <motion.button\n                    type=\"submit\"\n                    disabled={isSubmitting}\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                    className=\"w-full px-8 py-4 bg-primary text-white rounded-xl font-medium hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center gap-2\"\n                  >\n                    {isSubmitting ? (\n                      <>\n                        <div className=\"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\" />\n                        Sending...\n                      </>\n                    ) : (\n                      <>\n                        Send Message\n                        <Send size={18} />\n                      </>\n                    )}\n                  </motion.button>\n                </form>\n              </div>\n            </motion.div>\n\n            {/* Contact Info */}\n            <motion.div variants={itemVariants} className=\"space-y-8\">\n              {/* Contact Details */}\n              <div className=\"glass p-8 rounded-3xl\">\n                <h3 className=\"text-2xl font-semibold mb-6 text-foreground\">\n                  Contact Information\n                </h3>\n                \n                <div className=\"space-y-6\">\n                  {contactInfo.map((info) => (\n                    <motion.div\n                      key={info.title}\n                      whileHover={{ x: 5 }}\n                      className=\"flex items-center gap-4\"\n                    >\n                      <div className=\"p-3 bg-primary/10 text-primary rounded-xl\">\n                        {info.icon}\n                      </div>\n                      <div>\n                        <h4 className=\"font-medium text-foreground\">{info.title}</h4>\n                        {info.link ? (\n                          <a\n                            href={info.link}\n                            className=\"text-foreground/70 hover:text-primary transition-colors\"\n                          >\n                            {info.value}\n                          </a>\n                        ) : (\n                          <p className=\"text-foreground/70\">{info.value}</p>\n                        )}\n                      </div>\n                    </motion.div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Social Links */}\n              <div className=\"glass p-8 rounded-3xl\">\n                <h3 className=\"text-2xl font-semibold mb-6 text-foreground\">\n                  Follow Me\n                </h3>\n                \n                <div className=\"flex space-x-4\">\n                  {socialLinks.map((social) => (\n                    <motion.a\n                      key={social.name}\n                      href={social.url}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      whileHover={{ scale: 1.1, y: -5 }}\n                      whileTap={{ scale: 0.9 }}\n                      className=\"p-4 bg-foreground/5 hover:bg-primary/20 rounded-xl transition-all duration-200 group\"\n                      aria-label={social.name}\n                    >\n                      <div className=\"text-foreground group-hover:text-primary transition-colors\">\n                        {social.icon}\n                      </div>\n                    </motion.a>\n                  ))}\n                </div>\n              </div>\n\n              {/* Availability */}\n              <div className=\"glass p-8 rounded-3xl\">\n                <h3 className=\"text-2xl font-semibold mb-4 text-foreground\">\n                  Availability\n                </h3>\n                <div className=\"flex items-center gap-3 mb-4\">\n                  <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\"></div>\n                  <span className=\"text-foreground font-medium\">Available for new projects</span>\n                </div>\n                <p className=\"text-foreground/70 text-sm\">\n                  I'm currently accepting new freelance projects and full-time opportunities. \n                  Let's discuss how I can help bring your ideas to life!\n                </p>\n              </div>\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Contact;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,UAAU;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;IACX;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,eAAe;gBACf,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,2BAA2B;QAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,aAAa;QACb,YAAY;YAAE,MAAM;YAAI,OAAO;YAAI,SAAS;YAAI,SAAS;QAAG;QAC5D,gBAAgB;QAEhB,yDAAyD;QACzD,MAAM;IACR;IAEA,MAAM,cAAc;QAClB;YACE,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;YAClB,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,MAAM;;;;;;YACnB,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,oBAAM,8OAAC,0MAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;YACpB,OAAO;YACP,OAAO;YACP,MAAM;QACR;KACD;IAED,MAAM,cAAc;QAClB;YACE,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;YACpB,MAAM;YACN,KAAK;QACP;QACA;YACE,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,MAAM;;;;;;YACtB,MAAM;YACN,KAAK;QACP;QACA;YACE,oBAAM,8OAAC,wMAAA,CAAA,UAAO;gBAAC,MAAM;;;;;;YACrB,MAAM;YACN,KAAK;QACP;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;;0BAE9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAI;oBACpC,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,8OAAC;oCAAG,WAAU;;wCAAsC;sDAC3C,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;;;;;;;sCAM9D,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,UAAU;8CACpB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAI5D,8OAAC;gDAAK,UAAU;gDAAc,WAAU;;kEACtC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,SAAQ;wEAAO,WAAU;kFAAiD;;;;;;kFAGjF,8OAAC;wEACC,MAAK;wEACL,IAAG;wEACH,MAAK;wEACL,OAAO,SAAS,IAAI;wEACpB,UAAU;wEACV,QAAQ;wEACR,WAAU;wEACV,aAAY;;;;;;;;;;;;0EAIhB,8OAAC;;kFACC,8OAAC;wEAAM,SAAQ;wEAAQ,WAAU;kFAAiD;;;;;;kFAGlF,8OAAC;wEACC,MAAK;wEACL,IAAG;wEACH,MAAK;wEACL,OAAO,SAAS,KAAK;wEACrB,UAAU;wEACV,QAAQ;wEACR,WAAU;wEACV,aAAY;;;;;;;;;;;;;;;;;;kEAKlB,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAU,WAAU;0EAAiD;;;;;;0EAGpF,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,OAAO;gEACvB,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAU,WAAU;0EAAiD;;;;;;0EAGpF,8OAAC;gEACC,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,OAAO;gEACvB,UAAU;gEACV,QAAQ;gEACR,MAAM;gEACN,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wDACZ,MAAK;wDACL,UAAU;wDACV,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;wDACxB,WAAU;kEAET,6BACC;;8EACE,8OAAC;oEAAI,WAAU;;;;;;gEAA8E;;yFAI/F;;gEAAE;8EAEA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CASxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,UAAU;oCAAc,WAAU;;sDAE5C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA8C;;;;;;8DAI5D,8OAAC;oDAAI,WAAU;8DACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DAET,YAAY;gEAAE,GAAG;4DAAE;4DACnB,WAAU;;8EAEV,8OAAC;oEAAI,WAAU;8EACZ,KAAK,IAAI;;;;;;8EAEZ,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFAA+B,KAAK,KAAK;;;;;;wEACtD,KAAK,IAAI,iBACR,8OAAC;4EACC,MAAM,KAAK,IAAI;4EACf,WAAU;sFAET,KAAK,KAAK;;;;;iGAGb,8OAAC;4EAAE,WAAU;sFAAsB,KAAK,KAAK;;;;;;;;;;;;;2DAjB5C,KAAK,KAAK;;;;;;;;;;;;;;;;sDA0BvB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA8C;;;;;;8DAI5D,8OAAC;oDAAI,WAAU;8DACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4DAEP,MAAM,OAAO,GAAG;4DAChB,QAAO;4DACP,KAAI;4DACJ,YAAY;gEAAE,OAAO;gEAAK,GAAG,CAAC;4DAAE;4DAChC,UAAU;gEAAE,OAAO;4DAAI;4DACvB,WAAU;4DACV,cAAY,OAAO,IAAI;sEAEvB,cAAA,8OAAC;gEAAI,WAAU;0EACZ,OAAO,IAAI;;;;;;2DAVT,OAAO,IAAI;;;;;;;;;;;;;;;;sDAkBxB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA8C;;;;;;8DAG5D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAA8B;;;;;;;;;;;;8DAEhD,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1D;uCAEe", "debugId": null}}, {"offset": {"line": 3216, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/port/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Heart, ArrowUp } from 'lucide-react';\n\nconst Footer = () => {\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  const currentYear = new Date().getFullYear();\n\n  const quickLinks = [\n    { name: 'Home', href: '#home' },\n    { name: 'About', href: '#about' },\n    { name: 'Skills', href: '#skills' },\n    { name: 'Projects', href: '#projects' },\n    { name: 'Experience', href: '#experience' },\n    { name: 'Contact', href: '#contact' },\n  ];\n\n  return (\n    <footer className=\"relative overflow-hidden bg-foreground/5 border-t border-foreground/10\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 -z-10\">\n        <div className=\"absolute top-1/2 left-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-1/4 right-1/4 w-80 h-80 bg-secondary/5 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-6 py-12\">\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8\">\n          {/* Brand Section */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n            viewport={{ once: true }}\n            className=\"lg:col-span-2\"\n          >\n            <div className=\"mb-6\">\n              <h3 className=\"text-2xl font-bold gradient-text mb-2\">\n                Bhupender Yadav\n              </h3>\n              <p className=\"text-foreground/70 max-w-md\">\n                Web Developer & UI/UX Designer passionate about creating beautiful, \n                functional digital experiences that make a difference.\n              </p>\n            </div>\n            \n            <div className=\"flex items-center gap-2 text-foreground/60\">\n              <span>Made with</span>\n              <motion.div\n                animate={{ scale: [1, 1.2, 1] }}\n                transition={{ duration: 1, repeat: Infinity }}\n              >\n                <Heart size={16} className=\"text-red-500 fill-current\" />\n              </motion.div>\n              <span>using Next.js & Framer Motion</span>\n            </div>\n          </motion.div>\n\n          {/* Quick Links */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.1 }}\n            viewport={{ once: true }}\n          >\n            <h4 className=\"text-lg font-semibold text-foreground mb-4\">\n              Quick Links\n            </h4>\n            <ul className=\"space-y-2\">\n              {quickLinks.map((link) => (\n                <li key={link.name}>\n                  <motion.a\n                    href={link.href}\n                    whileHover={{ x: 5 }}\n                    className=\"text-foreground/70 hover:text-primary transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </motion.a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Contact Info */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.2 }}\n            viewport={{ once: true }}\n          >\n            <h4 className=\"text-lg font-semibold text-foreground mb-4\">\n              Get In Touch\n            </h4>\n            <div className=\"space-y-2 text-foreground/70\">\n              <p>\n                <a \n                  href=\"mailto:<EMAIL>\"\n                  className=\"hover:text-primary transition-colors duration-200\"\n                >\n                  <EMAIL>\n                </a>\n              </p>\n              <p>\n                <a \n                  href=\"tel:+15551234567\"\n                  className=\"hover:text-primary transition-colors duration-200\"\n                >\n                  +****************\n                </a>\n              </p>\n              <p>New York, NY</p>\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Bottom Section */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.5, delay: 0.3 }}\n          viewport={{ once: true }}\n          className=\"pt-8 border-t border-foreground/10\"\n        >\n          <div className=\"flex flex-col md:flex-row justify-between items-center gap-4\">\n            <p className=\"text-foreground/60 text-sm\">\n              © {currentYear} Bhupender Yadav. All rights reserved.\n            </p>\n            \n            <div className=\"flex items-center gap-6\">\n              <a \n                href=\"/privacy\" \n                className=\"text-foreground/60 hover:text-primary text-sm transition-colors duration-200\"\n              >\n                Privacy Policy\n              </a>\n              <a \n                href=\"/terms\" \n                className=\"text-foreground/60 hover:text-primary text-sm transition-colors duration-200\"\n              >\n                Terms of Service\n              </a>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Scroll to Top Button */}\n      <motion.button\n        onClick={scrollToTop}\n        initial={{ opacity: 0, scale: 0 }}\n        whileInView={{ opacity: 1, scale: 1 }}\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.9 }}\n        transition={{ duration: 0.3 }}\n        viewport={{ once: true }}\n        className=\"fixed bottom-8 right-8 p-3 bg-primary text-white rounded-full shadow-lg hover:bg-primary/90 transition-colors duration-200 z-50\"\n        aria-label=\"Scroll to top\"\n      >\n        <ArrowUp size={20} />\n      </motion.button>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKA,MAAM,SAAS;IACb,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAc,MAAM;QAAc;QAC1C;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwC;;;;;;0DAGtD,8OAAC;gDAAE,WAAU;0DAA8B;;;;;;;;;;;;kDAM7C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,OAAO;wDAAC;wDAAG;wDAAK;qDAAE;gDAAC;gDAC9B,YAAY;oDAAE,UAAU;oDAAG,QAAQ;gDAAS;0DAE5C,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAE7B,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAKV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAA6C;;;;;;kDAG3D,8OAAC;wCAAG,WAAU;kDACX,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;0DACC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oDACP,MAAM,KAAK,IAAI;oDACf,YAAY;wDAAE,GAAG;oDAAE;oDACnB,WAAU;8DAET,KAAK,IAAI;;;;;;+CANL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAcxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAA6C;;;;;;kDAG3D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DACC,cAAA,8OAAC;oDACC,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;0DAIH,8OAAC;0DACC,cAAA,8OAAC;oDACC,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;0DAIH,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;kCAMT,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,aAAa;4BAAE,SAAS;wBAAE;wBAC1B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;wCAA6B;wCACrC;wCAAY;;;;;;;8CAGjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;gBACT,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,aAAa;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBACpC,YAAY;oBAAE,OAAO;gBAAI;gBACzB,UAAU;oBAAE,OAAO;gBAAI;gBACvB,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,UAAU;oBAAE,MAAM;gBAAK;gBACvB,WAAU;gBACV,cAAW;0BAEX,cAAA,8OAAC,4MAAA,CAAA,UAAO;oBAAC,MAAM;;;;;;;;;;;;;;;;;AAIvB;uCAEe", "debugId": null}}]}