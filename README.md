# Bhupender Yadav - Portfolio Website

A modern, responsive portfolio website showcasing my work as a Web Developer and UI/UX Designer. Built with Next.js, TypeScript, Tailwind CSS, and Framer Motion.

## 🚀 Features

- **Modern Design**: Clean, professional design with glassmorphism effects
- **Responsive**: Fully responsive design that works on all devices
- **Dark/Light Mode**: Toggle between dark and light themes
- **Smooth Animations**: Beautiful animations powered by Framer Motion
- **Interactive**: Engaging user interactions and hover effects
- **SEO Optimized**: Proper meta tags and structured data
- **Performance**: Optimized for fast loading and smooth performance

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Font**: Inter (Google Fonts)

## 📱 Sections

1. **Hero** - Introduction with animated background elements
2. **About** - Personal background and philosophy
3. **Skills** - Technical skills with animated progress bars
4. **Projects** - Featured and other notable projects
5. **Experience** - Professional timeline with achievements
6. **Contact** - Contact form and social links
7. **Footer** - Additional information and links

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone https://github.com/bhupenderyadav/portfolio.git
cd portfolio
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🎨 Customization

### Personal Information
Update the following files with your information:
- `src/app/layout.tsx` - Meta tags and SEO information
- `src/components/Hero.tsx` - Name, title, and social links
- `src/components/About.tsx` - Personal background and stats
- `src/components/Skills.tsx` - Your technical skills
- `src/components/Projects.tsx` - Your projects and work
- `src/components/Experience.tsx` - Professional experience
- `src/components/Contact.tsx` - Contact information

### Styling
- Colors and themes: `src/app/globals.css`
- Component styles: Individual component files
- Tailwind config: `tailwind.config.js`

### Content
- Add your project images to the `public/` directory
- Update project URLs and GitHub links
- Customize the contact form action
- Add your resume/CV file to `public/resume.pdf`

## 📦 Build and Deploy

### Build for Production
```bash
npm run build
npm start
```

### Deploy on Vercel
1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Deploy with one click

### Deploy on Netlify
1. Build the project: `npm run build`
2. Upload the `out` folder to Netlify
3. Configure redirects if needed

## 🔧 Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions, issues, and feature requests are welcome! Feel free to check the [issues page](https://github.com/bhupenderyadav/portfolio/issues).

## 📞 Contact

- **Email**: <EMAIL>
- **LinkedIn**: [linkedin.com/in/bhupenderyadav](https://linkedin.com/in/bhupenderyadav)
- **GitHub**: [github.com/bhupenderyadav](https://github.com/bhupenderyadav)

---

⭐ Star this repo if you found it helpful!
