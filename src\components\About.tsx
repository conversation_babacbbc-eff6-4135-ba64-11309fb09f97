'use client';

import { motion } from 'framer-motion';
import { Code, Palette, Lightbulb, Users } from 'lucide-react';

const About = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  const features = [
    {
      icon: <Code size={32} />,
      title: 'Clean Code',
      description: 'Writing maintainable, scalable, and efficient code following best practices and modern standards.',
    },
    {
      icon: <Palette size={32} />,
      title: 'Creative Design',
      description: 'Crafting visually appealing and user-friendly interfaces that enhance user experience.',
    },
    {
      icon: <Lightbulb size={32} />,
      title: 'Problem Solving',
      description: 'Analyzing complex problems and developing innovative solutions that meet business needs.',
    },
    {
      icon: <Users size={32} />,
      title: 'Collaboration',
      description: 'Working effectively with cross-functional teams to deliver exceptional digital products.',
    },
  ];

  return (
    <section id="about" className="py-20 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-1/3 right-1/4 w-64 h-64 bg-secondary/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/3 left-1/4 w-80 h-80 bg-accent/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-6">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          className="max-w-6xl mx-auto"
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              About <span className="gradient-text">Me</span>
            </h2>
            <p className="text-xl text-foreground/70 max-w-3xl mx-auto">
              I'm a passionate web developer and UI/UX designer with a love for creating 
              digital experiences that are both beautiful and functional.
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Text Content */}
            <motion.div variants={itemVariants} className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-2xl font-semibold text-foreground">
                  Crafting Digital Experiences
                </h3>
                <p className="text-foreground/80 leading-relaxed">
                  With a strong foundation in both development and design, I bridge the gap 
                  between technical implementation and user experience. My journey in web 
                  development started with a curiosity about how things work, and has evolved 
                  into a passion for creating meaningful digital solutions.
                </p>
                <p className="text-foreground/80 leading-relaxed">
                  I specialize in modern web technologies including React, Next.js, and 
                  TypeScript, while also having a keen eye for design principles, user 
                  psychology, and accessibility. I believe that great products are born 
                  from the intersection of beautiful design and robust functionality.
                </p>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-6 pt-6">
                <div className="text-center">
                  <div className="text-3xl font-bold gradient-text">50+</div>
                  <div className="text-foreground/70">Projects Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold gradient-text">3+</div>
                  <div className="text-foreground/70">Years Experience</div>
                </div>
              </div>
            </motion.div>

            {/* Right Column - Features Grid */}
            <motion.div variants={itemVariants} className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  variants={itemVariants}
                  whileHover={{ scale: 1.05, y: -5 }}
                  className="p-6 glass rounded-2xl hover:bg-foreground/5 transition-all duration-300"
                >
                  <div className="text-primary mb-4">{feature.icon}</div>
                  <h4 className="text-lg font-semibold mb-2 text-foreground">
                    {feature.title}
                  </h4>
                  <p className="text-foreground/70 text-sm leading-relaxed">
                    {feature.description}
                  </p>
                </motion.div>
              ))}
            </motion.div>
          </div>

          {/* Philosophy */}
          <motion.div
            variants={itemVariants}
            className="mt-16 text-center"
          >
            <div className="glass p-8 rounded-3xl max-w-4xl mx-auto">
              <h3 className="text-2xl font-semibold mb-4 gradient-text">My Philosophy</h3>
              <p className="text-lg text-foreground/80 leading-relaxed">
                "Good design is not just what it looks like and feels like. Good design is how it works." 
                I believe in creating solutions that are not only visually appealing but also intuitive, 
                accessible, and performant. Every line of code and every design decision should serve 
                the user's needs and enhance their experience.
              </p>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
