<svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="600" height="400" fill="url(#gradient)"/>
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:0.1" />
    </linearGradient>
  </defs>
  <rect x="50" y="50" width="500" height="300" rx="10" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
  <circle cx="300" cy="200" r="40" fill="rgba(59,130,246,0.3)"/>
  <rect x="260" y="180" width="80" height="40" rx="5" fill="rgba(139,92,246,0.3)"/>
  <text x="300" y="280" text-anchor="middle" fill="rgba(255,255,255,0.7)" font-family="Arial, sans-serif" font-size="16">Project Preview</text>
</svg>
